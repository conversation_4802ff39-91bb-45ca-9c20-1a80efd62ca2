<!DOCTYPE html>
<html lang="en">
<head>
    <%- include('../layout/head') %>
    <title>Quản lý Dự án - <PERSON><PERSON> thống <PERSON> sát</title>

    <!-- Custom styles for survey system -->
    <link href="/css/survey-system.css" rel="stylesheet">
</head>

<body>

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <%- include('../layout/sidebar') %>

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Header -->
                <%- include('../layout/header') %>

                <!-- Begin Page Content -->
                <div class="container-fluid">

                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">Quản lý Dự án</h1>
                        <a href="/projects/create" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
                            <i class="fas fa-plus fa-sm text-white-50"></i> Tạo Dự án Mới
                        </a>
                    </div>

                    <!-- Content Row -->
                    <div class="row">
                        <div class="col-12">
                            <!-- DataTables Card -->
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Danh sách Dự án</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                                            <thead>
                                                <tr>
                                                    <th width="50px">
                                                        <input type="checkbox" id="select-all">
                                                    </th>
                                                    <th>Tên Dự án</th>
                                                    <th>Mô tả</th>
                                                    <th>Trạng thái</th>
                                                    <th>Ngày bắt đầu</th>
                                                    <th>Ngày kết thúc</th>
                                                    <th>Ngày tạo</th>
                                                    <th width="150px">Thao tác</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <!-- Data will be loaded via AJAX -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <%- include('../layout/footer') %>

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Survey System JS -->
    <script src="/js/survey-system.js"></script>

    <!-- Page level custom scripts -->
    <script>
        $(document).ready(function() {
            // Initialize DataTable
            var table = $('#dataTable').DataTable({
                "processing": true,
                "serverSide": true,
                "ajax": {
                    "url": "/projects/list",
                    "type": "POST"
                },
                "columns": [
                    { "data": 0, "orderable": false, "searchable": false },
                    { "data": 1, "orderable": true, "searchable": true },
                    { "data": 2, "orderable": false, "searchable": true },
                    { "data": 3, "orderable": true, "searchable": false },
                    { "data": 4, "orderable": true, "searchable": false },
                    { "data": 5, "orderable": true, "searchable": false },
                    { "data": 6, "orderable": true, "searchable": false },
                    { "data": 7, "orderable": false, "searchable": false }
                ],
                "order": [[6, "desc"]],
                "pageLength": 25,
                "language": {
                    url: '/vendor/datatables/vi.json'
                }
            });

            // Select all checkbox
            $('#select-all').on('click', function() {
                var rows = table.rows({ 'search': 'applied' }).nodes();
                $('input[type="checkbox"]', rows).prop('checked', this.checked);
            });

            // Handle individual checkbox clicks
            $('#dataTable tbody').on('change', 'input[type="checkbox"]', function() {
                if (!this.checked) {
                    var el = $('#select-all').get(0);
                    if (el && el.checked && ('indeterminate' in el)) {
                        el.indeterminate = true;
                    }
                }
            });

            // Make table global for utility functions
            window.table = table;
        });
    </script>

</body>

</html>
