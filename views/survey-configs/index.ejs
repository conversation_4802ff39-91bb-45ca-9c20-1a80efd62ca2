<!DOCTYPE html>
<html lang="en">
<head>
    <%- include('../layout/head') %>
    <title>Quản lý Khảo sát - <%= project.name %></title>

    <!-- Custom styles for survey system -->
    <link href="/css/survey-system.css" rel="stylesheet">

    <!-- Custom styles for this page -->
    <link href="/vendor/datatables/dataTables.bootstrap4.min.css" rel="stylesheet">
</head>

<body>

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <%- include('../layout/sidebar') %>

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Header -->
                <%- include('../layout/header') %>

                <!-- Begin Page Content -->
                <div class="container-fluid">

                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <div>
                            <h1 class="h3 mb-0 text-gray-800">Quản lý Khảo sát</h1>
                            <p class="mb-0 text-muted">Dự án: <strong><%= project.name %></strong></p>
                        </div>
                        <div>
                            <a href="/projects/<%= project.id %>/surveys/create" class="d-none d-sm-inline-block btn btn-sm btn-success shadow-sm">
                                <i class="fas fa-plus fa-sm text-white-50"></i> Tạo Khảo sát Mới
                            </a>
                            <a href="/projects" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
                                <i class="fas fa-arrow-left fa-sm text-white-50"></i> Quay lại
                            </a>
                        </div>
                    </div>

                    <!-- Project Info Card -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card border-left-primary shadow">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <h5 class="font-weight-bold text-primary"><%= project.name %></h5>
                                            <p class="text-gray-600"><%= project.description || 'Không có mô tả' %></p>
                                            <div class="row">
                                                <div class="col-sm-6">
                                                    <small class="text-muted">
                                                        <strong>Ngày bắt đầu:</strong> 
                                                        <%= project.start_date ? moment(project.start_date).format('DD/MM/YYYY') : 'Chưa xác định' %>
                                                    </small>
                                                </div>
                                                <div class="col-sm-6">
                                                    <small class="text-muted">
                                                        <strong>Ngày kết thúc:</strong> 
                                                        <%= project.end_date ? moment(project.end_date).format('DD/MM/YYYY') : 'Chưa xác định' %>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 text-right">
                                            <% 
                                            const statusText = project.status === 1 ? 'Hoạt động' : 
                                                             project.status === 0 ? 'Tạm dừng' : 'Đã xóa';
                                            const statusClass = project.status === 1 ? 'success' : 
                                                               project.status === 0 ? 'warning' : 'danger';
                                            %>
                                            <span class="badge badge-<%= statusClass %> badge-lg"><%= statusText %></span>
                                            <% if (project.google_sheet_url) { %>
                                            <br><br>
                                            <a href="<%= project.google_sheet_url %>" target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-table"></i> Xem Google Sheet
                                            </a>
                                            <% } %>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Content Row -->
                    <div class="row">
                        <div class="col-12">
                            <!-- DataTables Card -->
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Danh sách Khảo sát</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                                            <thead>
                                                <tr>
                                                    <th width="50px">
                                                        <input type="checkbox" id="select-all">
                                                    </th>
                                                    <th>Tên Khảo sát</th>
                                                    <th>Mô tả</th>
                                                    <th>URL Slug</th>
                                                    <th>Trạng thái</th>
                                                    <th>Ngày tạo</th>
                                                    <th width="200px">Thao tác</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <!-- Data will be loaded via AJAX -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <%- include('../layout/footer') %>

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Survey System JS -->
    <script src="/js/survey-system.js"></script>

    <!-- Page level custom scripts -->
    <script>
        $(document).ready(function() {
            // Initialize DataTable
            var table = $('#dataTable').DataTable({
                "processing": true,
                "serverSide": true,
                "ajax": {
                    "url": "/projects/<%= project.id %>/surveys/list",
                    "type": "POST"
                },
                "columns": [
                    { "data": 0, "orderable": false, "searchable": false },
                    { "data": 1, "orderable": true, "searchable": true },
                    { "data": 2, "orderable": false, "searchable": true },
                    { "data": 3, "orderable": false, "searchable": true },
                    { "data": 4, "orderable": true, "searchable": false },
                    { "data": 5, "orderable": true, "searchable": false },
                    { "data": 6, "orderable": false, "searchable": false }
                ],
                "order": [[5, "desc"]],
                "pageLength": 25,
                "language": {
                    url: '/vendor/datatables/vi.json'
                }
            });

            // Select all checkbox
            $('#select-all').on('click', function() {
                var rows = table.rows({ 'search': 'applied' }).nodes();
                $('input[type="checkbox"]', rows).prop('checked', this.checked);
            });

            // Handle individual checkbox clicks
            $('#dataTable tbody').on('change', 'input[type="checkbox"]', function() {
                if (!this.checked) {
                    var el = $('#select-all').get(0);
                    if (el && el.checked && ('indeterminate' in el)) {
                        el.indeterminate = true;
                    }
                }
            });

            // Make table global for utility functions
            window.table = table;
        });

        // Survey Config Actions
        function editSurveyConfig(id) {
            window.location.href = `/survey-configs/${id}/edit`;
        }

        function configFields(id) {
            window.location.href = `/survey-configs/${id}/fields`;
        }

        function formBuilder(id) {
            window.location.href = `/survey-configs/${id}/form-builder`;
        }

        function viewResponses(id) {
            window.location.href = `/survey-configs/${id}/responses`;
        }

        function copyLink(url) {
            navigator.clipboard.writeText(url).then(function() {
                alert('Link đã được copy vào clipboard!');
            }, function(err) {
                console.error('Could not copy text: ', err);
                // Fallback for older browsers
                const textArea = document.createElement("textarea");
                textArea.value = url;
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                try {
                    document.execCommand('copy');
                    alert('Link đã được copy vào clipboard!');
                } catch (err) {
                    alert('Không thể copy link. Vui lòng copy thủ công: ' + url);
                }
                document.body.removeChild(textArea);
            });
        }

        function deleteSurveyConfig(id) {
            if (confirm('Bạn có chắc chắn muốn xóa cấu hình khảo sát này?')) {
                $.ajax({
                    url: `/survey-configs/${id}`,
                    type: 'DELETE',
                    success: function(response) {
                        if (response.success) {
                            alert('Xóa thành công!');
                            table.ajax.reload();
                        } else {
                            alert('Có lỗi xảy ra: ' + response.message);
                        }
                    },
                    error: function() {
                        alert('Có lỗi xảy ra khi xóa!');
                    }
                });
            }
        }
    </script>

</body>

</html>
