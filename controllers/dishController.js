const commonService = require('../services/commonService'),
    securityService = require('../services/securityService');
const dataTableService = require('../services/dataTableService');

const dishController = {
    // Hiển thị danh sách món ăn
    list: (req, res) => {
        try {
            const errors = [];
            res.render('admin/mon-an/list', {
                user: req.user,
                errors: errors
            });
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            return res.render("error");
        }
    },
    
    // Hiển thị form tạo/sửa món ăn
    detail: async (req, res) => {
        const errors = [];
        const user = req.user;
        const id = req.params.id;
        let dishData = null;
        let dishFoods = [];
        
        try {
            if (id && id !== 'new') {
                // Lấy thông tin món ăn hiện tại
                const currentDishRes = await commonService.getAllDataTable('dishes', { id: id });
                if (currentDishRes.success && currentDishRes.data && currentDishRes.data.length > 0) {
                    dishData = currentDishRes.data[0];
                    
                    // Lấy danh sách thực phẩm trong món ăn
                    const dishFoodsRes = await commonService.getListTable(`
                        SELECT
                            df.*,
                            fi.name as food_name,
                            fi.code as food_code,
                            fi.type as food_type,
                            fi.type_year as food_type_year,
                            fi.ten as food_ten,
                            fi.edible as food_edible,
                            fi.protein as food_protein,
                            fi.animal_protein as food_animal_protein,
                            fi.unanimal_lipid as food_unanimal_lipid,
                            fi.energy as food_energy,
                            fi.water as food_water,
                            fi.fat as food_fat,
                            fi.carbohydrate as food_carbohydrate,
                            fi.fiber as food_fiber,
                            fi.ash as food_ash,
                            fi.calci as food_calci,
                            fi.phosphorous as food_phosphorous,
                            fi.fe as food_fe,
                            fi.zinc as food_zinc,
                            fi.sodium as food_sodium,
                            fi.potassium as food_potassium,
                            fi.magnesium as food_magnesium,
                            fi.manganese as food_manganese,
                            fi.copper as food_copper,
                            fi.selenium as food_selenium
                        FROM dish_foods df
                        LEFT JOIN food_info fi ON df.food_id = fi.id
                        WHERE df.dish_id = ?
                        ORDER BY df.order_index ASC, df.id ASC
                    `, [id]);
                    
                    if (dishFoodsRes.success && dishFoodsRes.data) {
                        // Tính toán dinh dưỡng theo khối lượng thực tế
                        dishFoods = dishFoodsRes.data.map(food => {
                            const ratio = food.weight / 100; // food_info lưu theo 100g
                            return {
                                ...food,
                                calculated_energy: (parseFloat(food.food_energy) || 0) * ratio,
                                calculated_protein: (parseFloat(food.food_protein) || 0) * ratio,
                                calculated_fat: (parseFloat(food.food_fat) || 0) * ratio,
                                calculated_carbohydrate: (parseFloat(food.food_carbohydrate) || 0) * ratio,
                                calculated_animal_protein: (parseFloat(food.food_animal_protein) || 0) * ratio,
                                calculated_unanimal_lipid: (parseFloat(food.food_unanimal_lipid) || 0) * ratio,
                                calculated_water: (parseFloat(food.food_water) || 0) * ratio,
                                calculated_fiber: (parseFloat(food.food_fiber) || 0) * ratio,
                                calculated_ash: (parseFloat(food.food_ash) || 0) * ratio,
                                calculated_calci: (parseFloat(food.food_calci) || 0) * ratio,
                                calculated_phosphorous: (parseFloat(food.food_phosphorous) || 0) * ratio,
                                calculated_fe: (parseFloat(food.food_fe) || 0) * ratio,
                                calculated_zinc: (parseFloat(food.food_zinc) || 0) * ratio,
                                calculated_sodium: (parseFloat(food.food_sodium) || 0) * ratio,
                                calculated_potassium: (parseFloat(food.food_potassium) || 0) * ratio,
                                calculated_magnesium: (parseFloat(food.food_magnesium) || 0) * ratio,
                                calculated_manganese: (parseFloat(food.food_manganese) || 0) * ratio,
                                calculated_copper: (parseFloat(food.food_copper) || 0) * ratio,
                                calculated_selenium: (parseFloat(food.food_selenium) || 0) * ratio
                            };
                        });
                    }
                }
            }
            
        } catch (error) {
            errors.push('Có lỗi xảy ra khi tải dữ liệu: ' + error.message);
            commonService.saveLog(req, error.message, error.stack);
        }
        
        res.render('admin/mon-an/index', {
            user: user,
            errors: errors,
            dishData: dishData,
            dishFoods: dishFoods,
            isEdit: id && id !== 'new'
        });
    },
    
    // API lấy danh sách món ăn cho DataTable
    listData: (req, res) => {
        // Cấu hình DataTable
        const config = {
            table: 'dishes',
            columns: ['id', 'name', 'description', 'category', 'created_at'],
            primaryKey: 'id',
            active: -1,
            activeOperator: '!=',
            filters: {},
            searchColumns: ['name', 'category'],
            columnsMapping: [
                'name', // column 1
                'description', // column 2
                'category', // column 3
                'food_count', // column 4
                'total_weight', // column 5
                'total_energy', // column 6
                'created_by_name', // column 7
                'created_at' // column 4
            ],
            defaultOrder: [
                { column: 'id', dir: 'DESC' }
            ],
            checkRole: false // Sẽ check manual bên dưới
        };

        // Kiểm tra quyền truy cập
        if (!req.user.isAdmin) {
            return res.json(dataTableService.createErrorResponse(req.body, 'Bạn không có quyền truy cập danh sách này!'));
        }

        // Function xử lý dữ liệu trước khi trả về
        const preprocessData = async (data) => {
            for (let item of data) {
                // Lấy thông tin người tạo
                if (item.created_by) {
                    const userRes = await commonService.getAllDataTable('user', { id: item.created_by });
                    if (userRes.success && userRes.data && userRes.data.length > 0) {
                        item.created_by_name = userRes.data[0].fullname;
                    } else {
                        item.created_by_name = 'N/A';
                    }
                } else {
                    item.created_by_name = 'N/A';
                }

                // Đếm số lượng thực phẩm trong món ăn
                const countRes = await commonService.getListTable('SELECT COUNT(*) as count FROM dish_foods WHERE dish_id = ?', [item.id]);
                if (countRes.success && countRes.data && countRes.data.length > 0) {
                    item.food_count = countRes.data[0].count;
                } else {
                    item.food_count = 0;
                }

                // Tính tổng khối lượng và năng lượng
                const dishTotalsRes = await commonService.getListTable(`
                    SELECT
                        SUM(df.weight) as total_weight,
                        SUM((fi.energy * df.weight / 100)) as total_energy
                    FROM dish_foods df
                    LEFT JOIN food_info fi ON df.food_id = fi.id
                    WHERE df.dish_id = ?
                `, [item.id]);

                if (dishTotalsRes.success && dishTotalsRes.data && dishTotalsRes.data.length > 0) {
                    item.total_weight = parseFloat(dishTotalsRes.data[0].total_weight || 0);
                    item.total_energy = parseFloat(dishTotalsRes.data[0].total_energy || 0);
                } else {
                    item.total_weight = 0;
                    item.total_energy = 0;
                }
            }
            return data;
        };

        // Xử lý request với preprocessData
        dataTableService.handleDataTableRequest(req, res, config, preprocessData);
    },
    
    // API tạo/cập nhật món ăn
    upsert: async (req, res) => {
        const resultData = {
            success: false,
            message: '',
            data: null
        };
        try {
            const validateRules = [
                { field: "name", type: "string", required: true, message: "Vui lòng nhập tên món ăn!" }
            ];
            
            const parameter = {
                name: req.body.name,
                description: req.body.description || '',
                category: req.body.category || '',
                created_by: req.user.id,
                campaign_id: req.user.campaign_id
            };
            
            // Validate input
            const errors = securityService.validateInput(parameter, validateRules, { returnType: 'array' });
            if (errors.length > 0) {
                resultData.message = errors.map(s => s.message).join(', ');
                return res.json(resultData);
            }
            
            // Parse danh sách thực phẩm
            let dishFoods = [];
            if (req.body.dish_foods) {
                try {
                    dishFoods = JSON.parse(req.body.dish_foods);
                } catch (e) {
                    resultData.message = 'Dữ liệu thực phẩm không hợp lệ!';
                    return res.json(resultData);
                }
            }
            
            const isCreate = !req.body.id;
            let responseData;
            let dishId;
            
            if (isCreate) {
                // Thêm mới món ăn
                responseData = await commonService.addRecordTable(parameter, 'dishes', true);
                if (responseData.success && responseData.data) {
                    dishId = responseData.data.insertId;
                    resultData.data = { id: dishId };
                }
            } else {
                dishId = req.body.id;
                delete parameter.created_by; // Không cập nhật created_by khi edit
                delete parameter.campaign_id; // Không cập nhật campaign_id khi edit
                // Cập nhật món ăn
                responseData = await commonService.updateRecordTable(parameter, { id: dishId }, 'dishes');
            }
            
            if (responseData.success) {
                // Xóa tất cả thực phẩm cũ trong món ăn
                await commonService.deleteRecordTable({ dish_id: dishId }, {}, 'dish_foods');
                
                // Thêm lại danh sách thực phẩm mới (không tính tổng số)
                for (let i = 0; i < dishFoods.length; i++) {
                    const food = dishFoods[i];
                    const weight = parseFloat(food.weight) || 0;
                    
                    if (food.food_id && weight > 0) {
                        // Thêm vào bảng dish_foods
                        await commonService.addRecordTable({
                            dish_id: dishId,
                            food_id: food.food_id,
                            weight: weight,
                            order_index: i
                        }, 'dish_foods');
                    }
                }
            }
            
            resultData.success = responseData.success;
            resultData.message = responseData.success 
                ? (isCreate ? 'Lưu thành công!' : 'Cập nhật thành công!')
                : responseData.message;
                
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            resultData.message = 'Đã xảy ra lỗi trong quá trình xử lý!';
        }
        
        res.json(resultData);
    },
    
    // API xóa món ăn
    delete: async (req, res) => {
        const resultData = {
            success: false,
            message: '',
            data: null,
            error: null
        };

        try {
            const { id } = req.params;
            const user = req.user;

            // Kiểm tra quyền truy cập
            if (!user.isAdmin) {
                throw new Error('Bạn không có quyền xóa danh sách này!');
            }

            // Kiểm tra ID
            if (!id) {
                throw new Error('Thiếu ID bản ghi!');
            }

            const recordId = parseInt(id, 10);
            if (isNaN(recordId)) {
                throw new Error('ID bản ghi không hợp lệ!');
            }

            // Xóa bản ghi (soft delete)
            const updateData = { active: -1 };
            const conditions = { id: recordId };
            
            const responseData = await commonService.updateRecordTable(updateData, conditions, 'dishes');

            if (!responseData || !responseData.success) {
                throw new Error('Không thể xóa bản ghi!');
            }

            resultData.success = responseData.success;
            resultData.message = 'Xóa món ăn thành công!';
            resultData.data = responseData.data || null;

            return res.status(200).json(resultData);

        } catch (error) {
            commonService.saveLog(req, error.message, error.stack)
            res.json(securityService.createErrorResponse(error.message || 'Đã xảy ra lỗi khi xử lý yêu cầu!', error, 500));
        }
    },
    
    // API lấy danh sách món ăn cho select
    getDishesForSelect: async (req, res) => {
        const resultData = {
            success: false,
            message: '',
            data: []
        };
        
        try {
            const conditions = securityService.applyRoleBasedFiltering(req.user, { active: 1 });

            // Lấy danh sách món ăn
            const dishesRes = await commonService.getAllDataTable('dishes', conditions);
            if (dishesRes.success && dishesRes.data) {
                resultData.success = true;
                resultData.data = dishesRes.data.map(dish => ({
                    value: dish.id,
                    label: dish.name,
                    description: dish.description,
                    category: dish.category
                }));
            } else {
                resultData.message = 'Không có dữ liệu món ăn';
            }
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack)
            resultData.message = 'Có lỗi xảy ra khi lấy danh sách món ăn';
        }
        
        res.json(resultData);
    },
    
    // API lấy chi tiết thực phẩm trong món ăn
    getDishFoods: async (req, res) => {
        const resultData = {
            success: false,
            message: '',
            data: []
        };
        
        try {
            const dishId = req.params.id;
            if (!dishId) {
                resultData.message = 'Thiếu ID món ăn';
                return res.json(resultData);
            }
            const conditions = securityService.applyRoleBasedFiltering(req.user, { active: 1, id: dishId });

            // Kiểm tra món ăn có tồn tại không
            const dishCheckRes = await commonService.getAllDataTable('dishes', conditions, {}, 'AND', 'id, name');
            if (!dishCheckRes.success || !dishCheckRes.data || dishCheckRes.data.length === 0) {
                resultData.message = 'Món ăn không tồn tại';
                return res.json(resultData);
            }
            
            // Kiểm tra dữ liệu trong dish_foods đơn giản
            const simpleDishFoodsRes = await commonService.getAllDataTable('dish_foods', { dish_id: dishId });
            console.log('simpleDishFoodsRes', simpleDishFoodsRes, !simpleDishFoodsRes.success, !simpleDishFoodsRes.data, simpleDishFoodsRes.data.length === 0);
            if (!simpleDishFoodsRes.success || !simpleDishFoodsRes.data || simpleDishFoodsRes.data.length === 0) {
                resultData.message = 'Không có dữ liệu thực phẩm trong món ăn';
                return res.json(resultData);
            }
            
            // Query đơn giản hóa - lấy tất cả trường từ food_info
            const dishFoodsRes = await commonService.getListTable(`
                SELECT
                    df.*,
                    fi.*,
                    fi.id as food_info_id
                FROM dish_foods df
                LEFT JOIN food_info fi ON df.food_id = fi.id
                WHERE df.dish_id = ?
                ORDER BY df.order_index ASC, df.id ASC
            `, [dishId]);
            console.log('dishFoodsRes', dishFoodsRes);
            if (dishFoodsRes.success && dishFoodsRes.data && dishFoodsRes.data.length > 0) {
                resultData.success = true;
                resultData.message = `Tìm thấy ${dishFoodsRes.data.length} thực phẩm trong món ăn`;
                
                // Tính toán dinh dưỡng theo khối lượng thực tế
                resultData.data = dishFoodsRes.data.map(food => {
                    const ratio = food.weight / 100; // food_info lưu theo 100g
                    return {
                        ...food,
                        calculated_energy: (parseFloat(food.energy) || 0) * ratio,
                        calculated_protein: (parseFloat(food.protein) || 0) * ratio,
                        calculated_fat: (parseFloat(food.fat) || 0) * ratio,
                        calculated_carbohydrate: (parseFloat(food.carbohydrate) || 0) * ratio,
                        calculated_animal_protein: (parseFloat(food.animal_protein) || 0) * ratio,
                        calculated_unanimal_lipid: (parseFloat(food.unanimal_lipid) || 0) * ratio,
                        calculated_water: (parseFloat(food.water) || 0) * ratio,
                        calculated_fiber: (parseFloat(food.fiber) || 0) * ratio,
                        calculated_ash: (parseFloat(food.ash) || 0) * ratio,
                        calculated_calci: (parseFloat(food.calci) || 0) * ratio,
                        calculated_phosphorous: (parseFloat(food.phosphorous) || 0) * ratio,
                        calculated_fe: (parseFloat(food.fe) || 0) * ratio,
                        calculated_zinc: (parseFloat(food.zinc) || 0) * ratio,
                        calculated_sodium: (parseFloat(food.sodium) || 0) * ratio,
                        calculated_potassium: (parseFloat(food.potassium) || 0) * ratio,
                        calculated_magnesium: (parseFloat(food.magnesium) || 0) * ratio,
                        calculated_manganese: (parseFloat(food.manganese) || 0) * ratio,
                        calculated_copper: (parseFloat(food.copper) || 0) * ratio,
                        calculated_selenium: (parseFloat(food.selenium) || 0) * ratio,
                        calculated_vitamin_a_rae: (parseFloat(food.vitamin_a_rae) || 0) * ratio,
                        calculated_vitamin_b1: (parseFloat(food.vitamin_b1) || 0) * ratio,
                        calculated_vitamin_b2: (parseFloat(food.vitamin_b2) || 0) * ratio,
                        calculated_vitamin_b6: (parseFloat(food.vitamin_b6) || 0) * ratio,
                        calculated_vitamin_b12: (parseFloat(food.vitamin_b12) || 0) * ratio,
                        calculated_vitamin_c: (parseFloat(food.vitamin_c) || 0) * ratio,
                        calculated_vitamin_e: (parseFloat(food.vitamin_e) || 0) * ratio,
                        calculated_vitamin_k: (parseFloat(food.vitamin_k) || 0) * ratio,
                        calculated_folate: (parseFloat(food.folate) || 0) * ratio,
                        calculated_total_fat: (parseFloat(food.total_fat) || 0) * ratio,
                        calculated_total_saturated_fat: (parseFloat(food.total_saturated_fat) || 0) * ratio,
                        calculated_mufa: (parseFloat(food.mufa) || 0) * ratio,
                        calculated_fufa: (parseFloat(food.fufa) || 0) * ratio,
                        calculated_cholesterol: (parseFloat(food.cholesterol) || 0) * ratio,
                        calculated_total_sugar: (parseFloat(food.total_sugar) || 0) * ratio,
                        calculated_glucose: (parseFloat(food.glucose) || 0) * ratio,
                        calculated_fructose: (parseFloat(food.fructose) || 0) * ratio,
                        calculated_sucrose: (parseFloat(food.sucrose) || 0) * ratio,
                        calculated_lactose: (parseFloat(food.lactose) || 0) * ratio,
                        calculated_maltose: (parseFloat(food.maltose) || 0) * ratio,
                        calculated_lysin: (parseFloat(food.lysin) || 0) * ratio,
                        calculated_methionin: (parseFloat(food.methionin) || 0) * ratio,
                        calculated_tryptophan: (parseFloat(food.tryptophan) || 0) * ratio,
                        calculated_phenylalanin: (parseFloat(food.phenylalanin) || 0) * ratio,
                        calculated_threonin: (parseFloat(food.threonin) || 0) * ratio,
                        calculated_isoleucine: (parseFloat(food.isoleucine) || 0) * ratio,
                        calculated_leucine: (parseFloat(food.leucine) || 0) * ratio,
                        calculated_valine: (parseFloat(food.valine) || 0) * ratio,
                        
                        // Thêm các trường cần thiết cho frontend
                        food_name: food.name,
                        food_code: food.code,
                        food_type: food.type,
                        food_type_year: food.type_year,
                        food_ten: food.ten,
                        food_edible: food.edible,
                        food_energy: food.energy,
                        food_protein: food.protein,
                        food_fat: food.fat,
                        food_carbohydrate: food.carbohydrate,
                        food_animal_protein: food.animal_protein,
                        food_unanimal_lipid: food.unanimal_lipid
                    };
                });
            } else {
                resultData.message = 'Không tìm thấy thông tin thực phẩm trong món ăn';
            }
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack)
            resultData.message = 'Có lỗi xảy ra khi lấy danh sách thực phẩm';
        }
        
        res.json(resultData);
    }
};

module.exports = dishController; 